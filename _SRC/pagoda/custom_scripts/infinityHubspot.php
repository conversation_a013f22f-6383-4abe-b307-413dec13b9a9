<?php
error_reporting(E_ERROR | E_PARSE);

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

require_once '../apiExample/bento.php';

// Environment configuration function
function getEnvironmentConfig() {
    $host = $_SERVER['HTTP_HOST'];

    if ($host === 'localhost:8080') {
        return [
            'instance' => 'rickyvoltz',
            'api_token' => '1ac04c9bfe10b64d1fa9cdff6216035d9795e2a1f46e3fb5df36d6c9a2ec1169fea0618fbc014a0ff8381a75974f1bb46c6e2c1dd9c40ff4db6594a122d9d5ca',
            'company_type_id' => 24,
            'contact_type_id' => 23,
            'email_contact_info_type_id' => 14,
            'lead_source_contact_info_type_id' => 17,
            'hq_id' => 3530,
            'client_notes_entity_type' => 63
        ];
    } else {
        return [
            'instance' => 'infinity',
            'api_token' => '102344ab0c1ee8d39394de54e2f0acef9b8b3097197143b856d747018555beef92a095b96a09338194a5ceb85091ab22ac04b16e56ce6b61b59f6cc8beaca011',
            'company_type_id' => 6004528,
            'contact_type_id' => 1929600,
            'email_contact_info_type_id' => 20600612,
            'lead_source_contact_info_type_id' => 20600613,
            'hq_id' => 1476971,
            'client_notes_entity_type' => 1826954
        ];
    }
}

// Environment configuration
$config = getEnvironmentConfig();
$bento = new BentoAPI($config['instance'], $config['api_token']);

// Parse HubSpot payload
$json = file_get_contents("php://input");
$data = json_decode($json, true);

$firstname = $data['firstname'] ?? '';
$lastname = $data['lastname'] ?? '';
$email = $data['email'] ?? '';
$dealname = $data['dealname'] ?? '';
$lead_source = $data['lead_source'] ?? '';
$hubspot_owner_id = $data['hubspot_owner_id'] ?? '';

// Forward incoming payload to first Pipedream endpoint
$ch1 = curl_init('https://eowsqc6detxmwqb.m.pipedream.net');
curl_setopt($ch1, CURLOPT_POST, true);
curl_setopt($ch1, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch1, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch1, CURLOPT_RETURNTRANSFER, true);
curl_exec($ch1);
curl_close($ch1);

// MailSpon email function - using Salesforce pattern
function httpPost($url, $data) {
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($curl);
    curl_close($curl);
    return $response;
}

try {
    // 1. Company Creation
    $company = $bento->create([
        'objectType' => 'companies',
        'objectData' => [
            'type' => $config['company_type_id'],
            'name' => $dealname,
            'parent' => $config['hq_id'],
            'tagged_with' => [$config['hq_id']],
            'data_source' => crc32('hubspot'),
            'data_source_id' => $hubspot_owner_id
        ]
    ]);

    // 2. Contact Creation
    $contact = $bento->create([
        'objectType' => 'contacts',
        'objectData' => [
            'type' => $config['contact_type_id'],
            'company' => $company['id'],
            'parent' => $company['id'],
            'name' => $firstname . ' ' . $lastname,
            'fname' => $firstname,
            'lname' => $lastname,
            'tagged_with' => [$config['hq_id'], $company['id']],
            'data_source' => crc32('hubspot'),
            'data_source_id' => $hubspot_owner_id
        ]
    ]);

    // 3. Email Contact Info Creation
    $email_contact_info = $bento->create([
        'objectType' => 'contact_info',
        'objectData' => [
            'object_id' => $contact['id'],
            'object_type' => 'contacts',
            'name' => 'Email Address',
            'title' => 'Email Address',
            'info' => $email,
            'type' => $config['email_contact_info_type_id'],
            'is_primary' => 'yes',
            'data_source' => crc32('hubspot'),
            'data_source_id' => $hubspot_owner_id
        ]
    ]);

    // 4. Lead Source Contact Info Creation (if provided)
    $lead_source_contact_info = null;
    if (!empty($lead_source)) {
        $lead_source_contact_info = $bento->create([
            'objectType' => 'contact_info',
            'objectData' => [
                'object_id' => $contact['id'],
                'object_type' => 'contacts',
                'name' => 'Lead Source',
                'title' => 'Lead Source',
                'info' => $lead_source,
                'type' => $config['lead_source_contact_info_type_id'],
                'is_primary' => 'no',
                'data_source' => crc32('hubspot'),
                'data_source_id' => $hubspot_owner_id
            ]
        ]);
    }

    // 5. Client Notes Creation with debugging
    $client_notes_entity = $bento->getById($config['client_notes_entity_type']);

    $bp_name = $client_notes_entity['bp_name'];
    $hubspot_details = '<h5><strong>HubSpot Lead Details:</strong></h5>';
    $hubspot_details .= '<h5>Deal Name: ' . $dealname . '</h5>';
    $hubspot_details .= '<h5>Contact: ' . $firstname . ' ' . $lastname . '</h5>';
    $hubspot_details .= '<h5>Email: ' . $email . '</h5>';
    $hubspot_details .= '<h5>Lead Source: ' . $lead_source . '</h5>';
    $hubspot_details .= '<h5>HubSpot Owner ID: ' . $hubspot_owner_id . '</h5>';
    $hubspot_details .= '<h5>Company ID: ' . $company['id'] . '</h5>';
    $hubspot_details .= '<h5>Contact ID: ' . $contact['id'] . '</h5>';
    $hubspot_details .= '<h5>Timestamp: ' . date('d F Y, h:i:s A') . '</h5>';

    $client_notes_data = [
        'name' => 'HubSpot Lead - ' . $contact['name'],
        'parent' => $contact['id'],
        'tagged_with' => [$config['hq_id'], $contact['id'], $company['id']],
        '_17' => $hubspot_details,
        'data_source' => crc32('hubspot'),
        'data_source_id' => $hubspot_owner_id
    ];

    $client_notes = $bento->create([
        'objectType' => "#". $bp_name,
        'objectData' => $client_notes_data
    ]);

    // Send notification via Pipedream (fuck the email system for now)
    $payload = array(
        "email_to" => "<EMAIL>",
        "subject" => "HubSpot Lead Processed",
        "body" => "Company: {$dealname}, Contact: {$firstname} {$lastname}, Email: {$email}"
    );
    httpPost("https://eowsqc6detxmwqb.m.pipedream.net", $payload);

} catch (Exception $e) {
    // Send error notification via Pipedream
    $error_payload = array(
        "email_to" => "<EMAIL>",
        "subject" => "HubSpot Error",
        "body" => $e->getMessage()
    );
    httpPost("https://eowsqc6detxmwqb.m.pipedream.net", $error_payload);

    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'debug_entity' => $debug_entity ?? null
    ]);
    exit;
}

// Success response - just the created records and their properties
$response = [
    'status' => 'success',
    'message' => 'HubSpot webhook processed successfully',
    'timestamp' => date('Y-m-d H:i:s'),
    'created_records' => [
        'company' => [
            'id' => $company['id'],
            'name' => $company['name'],
            'type' => $company['type'],
            'data_source' => $company['data_source'],
            'data_source_id' => $company['data_source_id']
        ],
        'contact' => [
            'id' => $contact['id'],
            'name' => $contact['name'],
            'fname' => $contact['fname'],
            'lname' => $contact['lname'],
            'company' => $contact['company'],
            'data_source' => $contact['data_source'],
            'data_source_id' => $contact['data_source_id']
        ],
        'email_contact_info' => [
            'id' => $email_contact_info['id'],
            'object_id' => $email_contact_info['object_id'],
            'info' => $email_contact_info['info'],
            'type' => $email_contact_info['type'],
            'is_primary' => $email_contact_info['is_primary']
        ],
        'lead_source_contact_info' => $lead_source_contact_info ? [
            'id' => $lead_source_contact_info['id'],
            'object_id' => $lead_source_contact_info['object_id'],
            'info' => $lead_source_contact_info['info'],
            'type' => $lead_source_contact_info['type']
        ] : null,
        'client_notes' => [
            'id' => $client_notes['id'],
            'name' => $client_notes['name'],
            'parent' => $client_notes['parent'],
            '_17' => $client_notes['_17'],
            'tagged_with' => $client_notes['tagged_with']
        ]
    ],
    'environment' => $config['instance']
];

// Send success response to second Pipedream endpoint
$ch2 = curl_init('https://eo8vqnch968fhpz.m.pipedream.net');
curl_setopt($ch2, CURLOPT_POST, true);
curl_setopt($ch2, CURLOPT_POSTFIELDS, json_encode($response));
curl_setopt($ch2, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_exec($ch2);
curl_close($ch2);

http_response_code(200);
echo json_encode($response, JSON_PRETTY_PRINT);

?>
